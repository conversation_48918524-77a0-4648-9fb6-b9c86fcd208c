#!/usr/bin/env python3
"""
Test to verify that failure debug statements are still working.
This creates a puzzle that should trigger some failures and backtracking.
"""

from main import FlowFreeSolver

def test_debug_failures():
    """Test a puzzle that should show failure debug statements."""
    print("=== Debug Failures Test ===")
    
    # Create a 3x3 puzzle that might require some backtracking
    pairs = {
        1: ((0, 0), (2, 2)),  # Diagonal connection
        2: ((0, 2), (2, 0)),  # Other diagonal - should conflict
        3: ((1, 0), (1, 2))   # Middle row connection
    }
    
    solver = FlowFreeSolver(3, 3, pairs)
    solver.print_current_grid("Debug Failures Test - 3x3")
    
    print("🚀 Starting solver (expecting some failures)...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found (this might be expected)")
        solver.print_current_grid("Final State (No Solution)")
        return False

if __name__ == "__main__":
    test_debug_failures()
