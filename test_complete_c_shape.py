#!/usr/bin/env python3
"""
Test case specifically designed to test complete C-shape detection (7+ points).
This test creates scenarios where a complete C-shape pattern would be formed.
"""

from main import FlowFreeSolver

def test_complete_c_shape_horizontal():
    """Test a complete C-shape: horizontal → vertical → horizontal (opposite direction)."""
    print("=== Complete C-Shape Test (Horizontal) ===")
    
    # Create a 5x5 puzzle that could lead to complete C-shape formation
    # The path should go: horizontal right → vertical down → horizontal left (opposite)
    # This would create a pattern like:
    # 1 - - - .
    # . . . | .
    # . . . | .
    # . - - - .
    # . . . 1 .
    pairs = {
        1: ((0, 0), (4, 3)),  # This could form a complete C-shape
    }
    
    # Create and display the puzzle
    solver = FlowFreeSolver(5, 5, pairs)
    solver.print_current_grid("Complete C-Shape Test (Horizontal)")
    
    # Solve the puzzle
    print("🚀 Starting solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found (expected if complete C-shape detection works)")
        solver.print_current_grid("Final State (No Solution)")
        return False

def test_complete_c_shape_vertical():
    """Test a complete C-shape: vertical → horizontal → vertical (opposite direction)."""
    print("\n=== Complete C-Shape Test (Vertical) ===")
    
    # Create a 5x5 puzzle that could lead to complete C-shape formation
    # The path should go: vertical down → horizontal right → vertical up (opposite)
    pairs = {
        1: ((0, 0), (2, 4)),  # This could form a complete C-shape
    }
    
    solver = FlowFreeSolver(5, 5, pairs)
    solver.print_current_grid("Complete C-Shape Test (Vertical)")
    
    print("🚀 Starting solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found (expected if complete C-shape detection works)")
        solver.print_current_grid("Final State (No Solution)")
        return False

def test_manual_c_shape_path():
    """Manually test the C-shape detection logic with a known problematic path."""
    print("\n=== Manual C-Shape Path Test ===")
    
    # Create a solver and manually test the C-shape detection
    solver = FlowFreeSolver(5, 5, {1: ((0, 0), (4, 4))})
    
    # Test a path that should form a complete C-shape
    # Pattern: (0,0) → (0,1) → (0,2) → (0,3) → (1,3) → (2,3) → (2,2) → (2,1) → (2,0)
    test_path = [
        (0, 0), (0, 1), (0, 2), (0, 3),  # horizontal right
        (1, 3), (2, 3),                   # vertical down  
        (2, 2), (2, 1), (2, 0)            # horizontal left (opposite direction)
    ]
    
    print(f"Testing path: {test_path}")
    
    # Test with 5 points (partial C-shape)
    partial_result = solver.creates_c_shape(test_path[:5])
    print(f"Partial C-shape (5 points) detected: {partial_result}")
    
    # Test with 7 points (complete C-shape)
    if len(test_path) >= 7:
        complete_result = solver.creates_c_shape(test_path[:7])
        print(f"Complete C-shape (7 points) detected: {complete_result}")
    
    # Test with full path
    full_result = solver.creates_c_shape(test_path)
    print(f"Full path C-shape detected: {full_result}")
    
    return partial_result or complete_result or full_result

def test_non_c_shape_path():
    """Test a path that should NOT be detected as a C-shape."""
    print("\n=== Non-C-Shape Path Test ===")
    
    solver = FlowFreeSolver(5, 5, {1: ((0, 0), (4, 4))})
    
    # Test a path that goes in a straight line (should not be C-shape)
    straight_path = [(0, 0), (0, 1), (0, 2), (0, 3), (0, 4), (1, 4), (2, 4)]
    print(f"Testing straight path: {straight_path}")
    
    result = solver.creates_c_shape(straight_path)
    print(f"C-shape detected in straight path: {result}")
    
    # Test a path that makes only one turn (L-shape, not C-shape)
    l_shape_path = [(0, 0), (0, 1), (0, 2), (1, 2), (2, 2), (3, 2), (4, 2)]
    print(f"Testing L-shape path: {l_shape_path}")
    
    l_result = solver.creates_c_shape(l_shape_path)
    print(f"C-shape detected in L-shape path: {l_result}")
    
    return not result and not l_result  # Success if neither is detected as C-shape

if __name__ == "__main__":
    print("Running complete C-shape detection tests...\n")
    
    # Run the tests
    test1_success = test_complete_c_shape_horizontal()
    test2_success = test_complete_c_shape_vertical()
    test3_success = test_manual_c_shape_path()
    test4_success = test_non_c_shape_path()
    
    # Summary
    print("\n" + "="*60)
    print("Test Results:")
    print(f"Complete C-Shape (Horizontal): {'✅ PASSED' if not test1_success else '❌ FAILED (found solution)'}")
    print(f"Complete C-Shape (Vertical): {'✅ PASSED' if not test2_success else '❌ FAILED (found solution)'}")
    print(f"Manual C-Shape Detection: {'✅ PASSED' if test3_success else '❌ FAILED'}")
    print(f"Non-C-Shape Detection: {'✅ PASSED' if test4_success else '❌ FAILED'}")
    
    if test3_success and test4_success:
        print("\n🎉 C-shape detection logic is working correctly!")
    else:
        print("\n⚠️  C-shape detection may need adjustment!")
