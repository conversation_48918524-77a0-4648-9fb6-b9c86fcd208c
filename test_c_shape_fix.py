#!/usr/bin/env python3
"""
Test the fixed C-shape detection logic to ensure it properly detects
C-shapes with two turns rather than just L-shapes with one turn.
"""

from main import FlowFreeSolver

def test_c_shape_detection_fix():
    """Test that the fixed C-shape detection properly identifies C-shapes vs L-shapes."""
    print("=== Testing Fixed C-Shape Detection ===")

    # Create a solver for testing
    solver = FlowFreeSolver(5, 5, {1: ((0, 0), (4, 4))})

    # Test 1: True C-shape pattern (horizontal start)
    # Pattern: 1 move horizontal → 2 moves vertical → 1 move horizontal opposite
    # (0,0) → (0,1) → (1,1) → (2,1) → (2,0) creates ┐ shape
    true_c_shape_horizontal = [(0, 0), (0, 1), (1, 1), (2, 1), (2, 0)]
    print(f"Testing true C-shape (horizontal): {true_c_shape_horizontal}")
    result1 = solver._is_partial_c_shape(true_c_shape_horizontal)
    print(f"Result: {result1} (should be True)")

    # Test 2: True C-shape pattern (vertical start)
    # Pattern: 1 move vertical → 2 moves horizontal → 1 move vertical opposite
    # (0,0) → (1,0) → (1,1) → (1,2) → (0,2) creates └ shape
    true_c_shape_vertical = [(0, 0), (1, 0), (1, 1), (1, 2), (0, 2)]
    print(f"\nTesting true C-shape (vertical): {true_c_shape_vertical}")
    result2 = solver._is_partial_c_shape(true_c_shape_vertical)
    print(f"Result: {result2} (should be True)")

    # Test 3: L-shape pattern (should NOT be detected as C-shape)
    # Pattern: 1 move horizontal → 2 moves vertical (no opposite turn)
    # (0,0) → (0,1) → (1,1) → (2,1) → (3,1)
    l_shape = [(0, 0), (0, 1), (1, 1), (2, 1), (3, 1)]
    print(f"\nTesting L-shape: {l_shape}")
    result3 = solver._is_partial_c_shape(l_shape)
    print(f"Result: {result3} (should be False)")

    # Test 4: Straight line (should NOT be detected)
    straight_line = [(0, 0), (0, 1), (0, 2), (0, 3), (0, 4)]
    print(f"\nTesting straight line: {straight_line}")
    result4 = solver._is_partial_c_shape(straight_line)
    print(f"Result: {result4} (should be False)")

    # Test 5: U-shape that goes back in same direction (should NOT be detected)
    # Pattern: 1 move horizontal → 2 moves vertical → 1 move horizontal same direction
    # (0,0) → (0,1) → (1,1) → (2,1) → (2,2)
    u_shape = [(0, 0), (0, 1), (1, 1), (2, 1), (2, 2)]
    print(f"\nTesting U-shape (same direction): {u_shape}")
    result5 = solver._is_partial_c_shape(u_shape)
    print(f"Result: {result5} (should be False)")

    # Test 6: Wrong pattern - 2 moves horizontal first (should NOT be detected)
    # Pattern: 2 moves horizontal → 1 move vertical → 1 move horizontal
    # (0,0) → (0,1) → (0,2) → (1,2) → (1,1)
    wrong_pattern = [(0, 0), (0, 1), (0, 2), (1, 2), (1, 1)]
    print(f"\nTesting wrong pattern (2 moves first): {wrong_pattern}")
    result6 = solver._is_partial_c_shape(wrong_pattern)
    print(f"Result: {result6} (should be False)")

    # Test 7: Another true C-shape (left direction)
    # Pattern: 1 move horizontal left → 2 moves vertical down → 1 move horizontal right
    # (2,2) → (2,1) → (3,1) → (4,1) → (4,2) creates ┌ shape
    true_c_shape_left = [(2, 2), (2, 1), (3, 1), (4, 1), (4, 2)]
    print(f"\nTesting true C-shape (left start): {true_c_shape_left}")
    result7 = solver._is_partial_c_shape(true_c_shape_left)
    print(f"Result: {result7} (should be True)")

    # Summary
    print(f"\n=== Test Results Summary ===")
    print(f"True C-shape (horizontal right): {result1} (expected: True)")
    print(f"True C-shape (vertical down): {result2} (expected: True)")
    print(f"L-shape: {result3} (expected: False)")
    print(f"Straight line: {result4} (expected: False)")
    print(f"U-shape (same direction): {result5} (expected: False)")
    print(f"Wrong pattern (2 moves first): {result6} (expected: False)")
    print(f"True C-shape (horizontal left): {result7} (expected: True)")

    # Check if all tests passed
    expected_results = [True, True, False, False, False, False, True]
    actual_results = [result1, result2, result3, result4, result5, result6, result7]

    if actual_results == expected_results:
        print("\n✅ All tests passed! C-shape detection is working correctly.")
        return True
    else:
        print(f"\n❌ Some tests failed!")
        print(f"Expected: {expected_results}")
        print(f"Actual:   {actual_results}")
        return False

def test_integration_with_solver():
    """Test that the fixed C-shape detection works in the context of the full solver."""
    print("\n=== Integration Test with Solver ===")
    
    # Create a puzzle that could potentially create C-shapes
    pairs = {
        1: ((0, 0), (2, 4))  # This path might try to create C-shapes
    }
    
    solver = FlowFreeSolver(3, 5, pairs)
    solver.print_current_grid("Integration Test Puzzle")
    
    print("🚀 Starting solver with fixed C-shape detection...")
    if solver.solve():
        print("✅ Puzzle solved successfully!")
        solver.print_solution()
        return True
    else:
        print("❌ No solution found (this might be expected if C-shape detection is working)")
        solver.print_current_grid("Final State")
        return False

if __name__ == "__main__":
    print("Testing the fixed C-shape detection logic...\n")
    
    # Run the tests
    test1_passed = test_c_shape_detection_fix()
    test2_passed = test_integration_with_solver()
    
    print(f"\n=== Final Results ===")
    print(f"C-shape detection tests: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Integration test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
